<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile App Design</title>
    <!-- Feather Icons -->
    <script src="https://unpkg.com/feather-icons"></script>
    <style>
        :root {
            --primary-color: #007bff;
            --secondary-color: #6c757d;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
            --background-color: #ffffff;
            --text-color: #212529;
            --border-color: #dee2e6;
            --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            --font-size: 16px;
            --border-radius: 8px;
            --shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-family);
            font-size: var(--font-size);
            color: var(--text-color);
            background-color: var(--background-color);
            line-height: 1.6;
        }

        .app-container {
            display: flex;
            height: 100vh;
            overflow: hidden;
            flex-direction: column;
        }

        /* Top Navbar */
        .top-navbar {
            background-color: var(--background-color);
            border-bottom: 1px solid var(--border-color);
            padding: 0 20px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 999;
            box-shadow: var(--shadow);
        }

        .navbar-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .navbar-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 12px;
            border-radius: var(--border-radius);
            background-color: var(--light-color);
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 14px;
        }

        .notification-btn {
            position: relative;
            background: none;
            border: none;
            padding: 8px;
            border-radius: var(--border-radius);
            cursor: pointer;
            color: var(--text-color);
            transition: all 0.3s ease;
        }

        .notification-btn:hover {
            background-color: var(--light-color);
        }

        .notification-badge {
            position: absolute;
            top: 2px;
            right: 2px;
            background-color: var(--danger-color);
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }

        .hamburger-btn {
            background: none;
            border: none;
            padding: 8px;
            border-radius: var(--border-radius);
            cursor: pointer;
            color: var(--text-color);
            display: none;
        }

        .main-wrapper {
            display: flex;
            flex: 1;
            overflow: hidden;
        }

        /* Sidebar Navigation */
        .sidebar {
            width: 250px;
            background-color: var(--dark-color);
            color: white;
            padding: 20px 0;
            transform: translateX(-100%);
            transition: transform 0.3s ease;
            position: fixed;
            height: 100vh;
            z-index: 1000;
        }

        .sidebar.active {
            transform: translateX(0);
        }

        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            margin-bottom: 20px;
        }

        .sidebar-nav {
            list-style: none;
        }

        .sidebar-nav li {
            margin-bottom: 5px;
        }

        .sidebar-nav a {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 20px;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .sidebar-nav a:hover,
        .sidebar-nav a.active {
            background-color: var(--primary-color);
            color: white;
        }

        .sidebar-nav a i {
            width: 20px;
            height: 20px;
        }

        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: var(--dark-color);
            display: flex;
            justify-content: space-around;
            padding: 10px 0;
            z-index: 1000;
        }

        .bottom-nav a {
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            text-align: center;
            padding: 8px;
            border-radius: var(--border-radius);
            transition: all 0.3s ease;
            flex: 1;
            max-width: 80px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
        }

        .bottom-nav a:hover,
        .bottom-nav a.active {
            background-color: var(--primary-color);
            color: white;
        }

        .bottom-nav a i {
            width: 20px;
            height: 20px;
        }

        .bottom-nav a span {
            font-size: 12px;
        }

        /* Notifications Dropdown */
        .notifications-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background-color: var(--background-color);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            width: 300px;
            max-height: 400px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }

        .notifications-dropdown.show {
            display: block;
        }

        .notifications-header {
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .notification-item {
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .notification-item:hover {
            background-color: var(--light-color);
        }

        .notification-item:last-child {
            border-bottom: none;
        }

        .notification-item.unread {
            background-color: rgba(0, 123, 255, 0.05);
            border-left: 3px solid var(--primary-color);
        }

        .notification-title {
            font-weight: 600;
            margin-bottom: 5px;
        }

        .notification-message {
            font-size: 14px;
            color: var(--secondary-color);
            margin-bottom: 5px;
        }

        .notification-time {
            font-size: 12px;
            color: var(--secondary-color);
        }

        /* Main Content */
        .main-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            margin-bottom: 70px;
        }

        .page {
            display: none;
        }

        .page.active {
            display: block;
        }

        /* Login Page */
        .login-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
        }

        .login-form {
            background: white;
            padding: 40px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            width: 100%;
            max-width: 400px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
        }

        .form-control {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: var(--font-size);
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: var(--font-size);
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            gap: 8px;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: #0056b3;
        }

        .btn-success {
            background-color: var(--success-color);
            color: white;
        }

        .btn-danger {
            background-color: var(--danger-color);
            color: white;
        }

        .btn-secondary {
            background-color: var(--secondary-color);
            color: white;
        }

        /* Table */
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .table th {
            background-color: var(--light-color);
            font-weight: 600;
        }

        /* Modal */
        .modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal.active {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background-color: white;
            padding: 30px;
            border-radius: var(--border-radius);
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .close {
            font-size: 24px;
            cursor: pointer;
            color: var(--secondary-color);
        }

        /* Toast */
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: var(--border-radius);
            color: white;
            z-index: 3000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }

        .toast.show {
            transform: translateX(0);
        }

        .toast.success {
            background-color: var(--success-color);
        }

        .toast.error {
            background-color: var(--danger-color);
        }

        .toast.info {
            background-color: var(--info-color);
        }

        /* Settings */
        .settings-group {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
        }

        .settings-group h3 {
            margin-bottom: 15px;
            color: var(--primary-color);
        }

        .color-picker {
            width: 50px;
            height: 40px;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
        }

        /* Responsive Design */
        @media (min-width: 768px) {
            .sidebar {
                position: static;
                transform: translateX(0);
            }

            .bottom-nav {
                display: none;
            }

            .main-content {
                margin-bottom: 0;
                margin-left: 0;
            }

            .hamburger-btn {
                display: none;
            }
        }

        @media (max-width: 767px) {
            .main-content {
                padding: 15px;
                margin-bottom: 70px;
            }

            .login-form {
                padding: 30px 20px;
                margin: 20px;
            }

            .modal-content {
                margin: 20px;
                padding: 20px;
            }

            .table {
                font-size: 14px;
            }

            .table th,
            .table td {
                padding: 8px;
            }

            .hamburger-btn {
                display: block;
            }

            .user-info span {
                display: none;
            }

            .notifications-dropdown {
                width: 280px;
                right: -20px;
            }
        }
    </style>
</head>
<body>
    <!-- Login Page -->
    <div id="loginPage" class="login-container">
        <form class="login-form" onsubmit="login(event)">
            <h2 style="text-align: center; margin-bottom: 30px; color: var(--primary-color);">Login</h2>
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" class="form-control" required>
            </div>
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" class="form-control" required>
            </div>
            <button type="submit" class="btn btn-primary" style="width: 100%;">Login</button>
        </form>
    </div>

    <!-- Main App -->
    <div id="mainApp" class="app-container" style="display: none;">
        <!-- Top Navbar -->
        <nav class="top-navbar">
            <div class="navbar-left">
                <button class="hamburger-btn" onclick="toggleSidebar()">
                    <i data-feather="menu"></i>
                </button>
                <h3>My App</h3>
            </div>
            <div class="navbar-right">
                <div class="notification-btn" onclick="toggleNotifications()">
                    <i data-feather="bell"></i>
                    <span class="notification-badge" id="notificationBadge">3</span>
                    <!-- Notifications Dropdown -->
                    <div class="notifications-dropdown" id="notificationsDropdown">
                        <div class="notifications-header">
                            <span>Notifications</span>
                            <button onclick="markAllAsRead()" style="background: none; border: none; color: var(--primary-color); cursor: pointer; font-size: 12px;">Mark all as read</button>
                        </div>
                        <div class="notification-item unread">
                            <div class="notification-title">New User Registered</div>
                            <div class="notification-message">John Smith has joined the platform</div>
                            <div class="notification-time">2 minutes ago</div>
                        </div>
                        <div class="notification-item unread">
                            <div class="notification-title">System Update</div>
                            <div class="notification-message">New features are now available</div>
                            <div class="notification-time">1 hour ago</div>
                        </div>
                        <div class="notification-item unread">
                            <div class="notification-title">Backup Complete</div>
                            <div class="notification-message">Daily backup has been completed successfully</div>
                            <div class="notification-time">3 hours ago</div>
                        </div>
                    </div>
                </div>
                <div class="user-info">
                    <div class="user-avatar" id="userAvatar">JD</div>
                    <span id="userName">John Doe</span>
                    <i data-feather="chevron-down" style="width: 16px; height: 16px;"></i>
                </div>
            </div>
        </nav>

        <div class="main-wrapper">
            <!-- Sidebar -->
            <nav class="sidebar" id="sidebar">
                <div class="sidebar-header">
                    <h3>My App</h3>
                </div>
                <ul class="sidebar-nav">
                    <li><a href="#" onclick="showPage('dashboard')" class="nav-link active">
                        <i data-feather="home"></i>
                        <span>Dashboard</span>
                    </a></li>
                    <li><a href="#" onclick="showPage('users')" class="nav-link">
                        <i data-feather="users"></i>
                        <span>Users</span>
                    </a></li>
                    <li><a href="#" onclick="showPage('settings')" class="nav-link">
                        <i data-feather="settings"></i>
                        <span>Settings</span>
                    </a></li>
                    <li><a href="#" onclick="logout()">
                        <i data-feather="log-out"></i>
                        <span>Logout</span>
                    </a></li>
                </ul>
            </nav>

            <!-- Main Content -->
            <main class="main-content">
            <!-- Dashboard Page -->
            <div id="dashboard" class="page active">
                <h1>Dashboard</h1>
                <p>Welcome to your dashboard! This is a responsive mobile app design.</p>
                <div style="margin-top: 20px;">
                    <button class="btn btn-primary" onclick="showToast('Welcome to the dashboard!', 'info')">Show Info Toast</button>
                    <button class="btn btn-success" onclick="showToast('Success message!', 'success')">Show Success Toast</button>
                    <button class="btn btn-danger" onclick="showToast('Error message!', 'error')">Show Error Toast</button>
                </div>
            </div>

            <!-- Users Page -->
            <div id="users" class="page">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h1>Users</h1>
                    <button class="btn btn-primary" onclick="showModal('addUserModal')">
                        <i data-feather="user-plus" style="width: 16px; height: 16px;"></i>
                        Add User
                    </button>
                </div>
                <table class="table" id="usersTable">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>1</td>
                            <td>John Doe</td>
                            <td><EMAIL></td>
                            <td>
                                <button class="btn btn-danger" onclick="confirmDelete(1)">
                                    <i data-feather="trash-2" style="width: 16px; height: 16px;"></i>
                                    Delete
                                </button>
                            </td>
                        </tr>
                        <tr>
                            <td>2</td>
                            <td>Jane Smith</td>
                            <td><EMAIL></td>
                            <td>
                                <button class="btn btn-danger" onclick="confirmDelete(2)">
                                    <i data-feather="trash-2" style="width: 16px; height: 16px;"></i>
                                    Delete
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Settings Page -->
            <div id="settings" class="page">
                <h1>Settings</h1>
                
                <div class="settings-group">
                    <h3>Colors</h3>
                    <div class="form-group">
                        <label for="primaryColor">Primary Color</label>
                        <input type="color" id="primaryColor" class="color-picker" value="#007bff" onchange="updateColor('--primary-color', this.value)">
                    </div>
                    <div class="form-group">
                        <label for="backgroundColor">Background Color</label>
                        <input type="color" id="backgroundColor" class="color-picker" value="#ffffff" onchange="updateColor('--background-color', this.value)">
                    </div>
                    <div class="form-group">
                        <label for="textColor">Text Color</label>
                        <input type="color" id="textColor" class="color-picker" value="#212529" onchange="updateColor('--text-color', this.value)">
                    </div>
                </div>

                <div class="settings-group">
                    <h3>Typography</h3>
                    <div class="form-group">
                        <label for="fontFamily">Font Family</label>
                        <select id="fontFamily" class="form-control" onchange="updateFont(this.value)">
                            <option value="'Segoe UI', Tahoma, Geneva, Verdana, sans-serif">Segoe UI</option>
                            <option value="Arial, sans-serif">Arial</option>
                            <option value="'Times New Roman', serif">Times New Roman</option>
                            <option value="'Courier New', monospace">Courier New</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="fontSize">Font Size</label>
                        <select id="fontSize" class="form-control" onchange="updateFontSize(this.value)">
                            <option value="14px">Small (14px)</option>
                            <option value="16px" selected>Medium (16px)</option>
                            <option value="18px">Large (18px)</option>
                            <option value="20px">Extra Large (20px)</option>
                        </select>
                    </div>
                </div>

                <button class="btn btn-success" onclick="saveSettings()">
                    <i data-feather="save" style="width: 16px; height: 16px;"></i>
                    Save Settings
                </button>
                <button class="btn btn-secondary" onclick="resetSettings()">
                    <i data-feather="refresh-cw" style="width: 16px; height: 16px;"></i>
                    Reset to Default
                </button>
            </div>
            </main>
        </div>

        <!-- Bottom Navigation -->
        <nav class="bottom-nav">
            <a href="#" onclick="showPage('dashboard')" class="nav-link active">
                <i data-feather="home"></i>
                <span>Dashboard</span>
            </a>
            <a href="#" onclick="showPage('users')" class="nav-link">
                <i data-feather="users"></i>
                <span>Users</span>
            </a>
            <a href="#" onclick="showPage('settings')" class="nav-link">
                <i data-feather="settings"></i>
                <span>Settings</span>
            </a>
            <a href="#" onclick="logout()">
                <i data-feather="log-out"></i>
                <span>Logout</span>
            </a>
        </nav>
    </div>

    <!-- Add User Modal -->
    <div id="addUserModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Add New User</h3>
                <span class="close" onclick="hideModal('addUserModal')">&times;</span>
            </div>
            <form onsubmit="addUser(event)">
                <div class="form-group">
                    <label for="newUserName">Name</label>
                    <input type="text" id="newUserName" class="form-control" required>
                </div>
                <div class="form-group">
                    <label for="newUserEmail">Email</label>
                    <input type="email" id="newUserEmail" class="form-control" required>
                </div>
                <div style="text-align: right; margin-top: 20px;">
                    <button type="button" class="btn btn-secondary" onclick="hideModal('addUserModal')">
                        <i data-feather="x" style="width: 16px; height: 16px;"></i>
                        Cancel
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i data-feather="user-plus" style="width: 16px; height: 16px;"></i>
                        Add User
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Confirm Modal -->
    <div id="confirmModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Confirm Action</h3>
                <span class="close" onclick="hideModal('confirmModal')">&times;</span>
            </div>
            <p id="confirmMessage">Are you sure you want to perform this action?</p>
            <div style="text-align: right; margin-top: 20px;">
                <button type="button" class="btn btn-secondary" onclick="hideModal('confirmModal')">
                    <i data-feather="x" style="width: 16px; height: 16px;"></i>
                    Cancel
                </button>
                <button type="button" class="btn btn-danger" id="confirmButton">
                    <i data-feather="check" style="width: 16px; height: 16px;"></i>
                    Confirm
                </button>
            </div>
        </div>
    </div>

    <!-- Alert Modal -->
    <div id="alertModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="alertTitle">Alert</h3>
                <span class="close" onclick="hideModal('alertModal')">&times;</span>
            </div>
            <p id="alertMessage">This is an alert message.</p>
            <div style="text-align: right; margin-top: 20px;">
                <button type="button" class="btn btn-primary" onclick="hideModal('alertModal')">
                    <i data-feather="check" style="width: 16px; height: 16px;"></i>
                    OK
                </button>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div id="toastContainer"></div>

    <script>
        let currentUser = null;
        let users = [
            { id: 1, name: 'John Doe', email: '<EMAIL>' },
            { id: 2, name: 'Jane Smith', email: '<EMAIL>' }
        ];
        let nextUserId = 3;

        // Login functionality
        function login(event) {
            event.preventDefault();
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            // Simple validation (in real app, this would be server-side)
            if (username && password) {
                currentUser = username;
                document.getElementById('loginPage').style.display = 'none';
                document.getElementById('mainApp').style.display = 'flex';
                showToast('Login successful!', 'success');
                loadSettings();
                updateUserInfo(username);
                feather.replace(); // Initialize feather icons
            } else {
                showToast('Please enter valid credentials', 'error');
            }
        }

        // Update user info in navbar
        function updateUserInfo(username) {
            const initials = username.split(' ').map(name => name[0]).join('').toUpperCase();
            document.getElementById('userAvatar').textContent = initials;
            document.getElementById('userName').textContent = username;
        }

        // Logout functionality
        function logout() {
            currentUser = null;
            document.getElementById('loginPage').style.display = 'flex';
            document.getElementById('mainApp').style.display = 'none';
            showToast('Logged out successfully', 'info');
        }

        // Page navigation
        function showPage(pageId) {
            // Hide all pages
            const pages = document.querySelectorAll('.page');
            pages.forEach(page => page.classList.remove('active'));

            // Show selected page
            document.getElementById(pageId).classList.add('active');

            // Update navigation active states
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => link.classList.remove('active'));

            // Find and activate the correct nav link
            const activeLinks = document.querySelectorAll(`[onclick*="${pageId}"]`);
            activeLinks.forEach(link => link.classList.add('active'));

            // Close sidebar on mobile after navigation
            if (window.innerWidth < 768) {
                document.getElementById('sidebar').classList.remove('active');
            }
        }

        // Modal functionality
        function showModal(modalId) {
            document.getElementById(modalId).classList.add('active');
        }

        function hideModal(modalId) {
            document.getElementById(modalId).classList.remove('active');
        }

        // Toast functionality
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.textContent = message;

            document.body.appendChild(toast);

            // Show toast
            setTimeout(() => toast.classList.add('show'), 100);

            // Hide and remove toast
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => document.body.removeChild(toast), 300);
            }, 3000);
        }

        // User management
        function addUser(event) {
            event.preventDefault();
            const name = document.getElementById('newUserName').value;
            const email = document.getElementById('newUserEmail').value;

            const newUser = {
                id: nextUserId++,
                name: name,
                email: email
            };

            users.push(newUser);
            updateUsersTable();
            hideModal('addUserModal');
            showToast('User added successfully!', 'success');

            // Reset form
            document.getElementById('newUserName').value = '';
            document.getElementById('newUserEmail').value = '';
        }

        function confirmDelete(userId) {
            const user = users.find(u => u.id === userId);
            if (user) {
                document.getElementById('confirmMessage').textContent =
                    `Are you sure you want to delete user "${user.name}"?`;
                document.getElementById('confirmButton').onclick = () => deleteUser(userId);
                showModal('confirmModal');
            }
        }

        function deleteUser(userId) {
            users = users.filter(u => u.id !== userId);
            updateUsersTable();
            hideModal('confirmModal');
            showToast('User deleted successfully!', 'success');
        }

        function updateUsersTable() {
            const tbody = document.querySelector('#usersTable tbody');
            tbody.innerHTML = '';

            users.forEach(user => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${user.id}</td>
                    <td>${user.name}</td>
                    <td>${user.email}</td>
                    <td>
                        <button class="btn btn-danger" onclick="confirmDelete(${user.id})">
                            <i data-feather="trash-2" style="width: 16px; height: 16px;"></i>
                            Delete
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });

            // Re-initialize feather icons for new elements
            feather.replace();
        }

        // Settings functionality
        function updateColor(property, value) {
            document.documentElement.style.setProperty(property, value);
        }

        function updateFont(fontFamily) {
            document.documentElement.style.setProperty('--font-family', fontFamily);
        }

        function updateFontSize(fontSize) {
            document.documentElement.style.setProperty('--font-size', fontSize);
        }

        function saveSettings() {
            const settings = {
                primaryColor: document.getElementById('primaryColor').value,
                backgroundColor: document.getElementById('backgroundColor').value,
                textColor: document.getElementById('textColor').value,
                fontFamily: document.getElementById('fontFamily').value,
                fontSize: document.getElementById('fontSize').value
            };

            localStorage.setItem('appSettings', JSON.stringify(settings));
            showToast('Settings saved successfully!', 'success');
        }

        function loadSettings() {
            const savedSettings = localStorage.getItem('appSettings');
            if (savedSettings) {
                const settings = JSON.parse(savedSettings);

                document.getElementById('primaryColor').value = settings.primaryColor;
                document.getElementById('backgroundColor').value = settings.backgroundColor;
                document.getElementById('textColor').value = settings.textColor;
                document.getElementById('fontFamily').value = settings.fontFamily;
                document.getElementById('fontSize').value = settings.fontSize;

                updateColor('--primary-color', settings.primaryColor);
                updateColor('--background-color', settings.backgroundColor);
                updateColor('--text-color', settings.textColor);
                updateFont(settings.fontFamily);
                updateFontSize(settings.fontSize);
            }
        }

        function resetSettings() {
            document.documentElement.style.setProperty('--primary-color', '#007bff');
            document.documentElement.style.setProperty('--background-color', '#ffffff');
            document.documentElement.style.setProperty('--text-color', '#212529');
            document.documentElement.style.setProperty('--font-family', "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif");
            document.documentElement.style.setProperty('--font-size', '16px');

            document.getElementById('primaryColor').value = '#007bff';
            document.getElementById('backgroundColor').value = '#ffffff';
            document.getElementById('textColor').value = '#212529';
            document.getElementById('fontFamily').value = "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif";
            document.getElementById('fontSize').value = '16px';

            localStorage.removeItem('appSettings');
            showToast('Settings reset to default!', 'info');
        }

        // Mobile sidebar toggle
        function toggleSidebar() {
            document.getElementById('sidebar').classList.toggle('active');
        }

        // Notifications functionality
        function toggleNotifications() {
            const dropdown = document.getElementById('notificationsDropdown');
            dropdown.classList.toggle('show');
        }

        function markAllAsRead() {
            const unreadItems = document.querySelectorAll('.notification-item.unread');
            unreadItems.forEach(item => item.classList.remove('unread'));
            document.getElementById('notificationBadge').style.display = 'none';
            showToast('All notifications marked as read', 'info');
        }

        // Close notifications when clicking outside
        document.addEventListener('click', function(event) {
            const notificationBtn = document.querySelector('.notification-btn');
            const dropdown = document.getElementById('notificationsDropdown');

            if (!notificationBtn.contains(event.target)) {
                dropdown.classList.remove('show');
            }
        });

        // Alert functionality
        function showAlert(title, message) {
            document.getElementById('alertTitle').textContent = title;
            document.getElementById('alertMessage').textContent = message;
            showModal('alertModal');
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.classList.remove('active');
                }
            });
        }

        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize feather icons
            feather.replace();

            // Add demo buttons to dashboard for testing alerts
            const dashboard = document.getElementById('dashboard');
            const alertDemo = document.createElement('div');
            alertDemo.style.marginTop = '20px';
            alertDemo.innerHTML = `
                <h3>Demo Features:</h3>
                <button class="btn btn-info" onclick="showAlert('Information', 'This is an information alert!')">
                    <i data-feather="info" style="width: 16px; height: 16px; margin-right: 8px;"></i>
                    Show Alert
                </button>
                <button class="btn btn-warning" onclick="showModal('confirmModal'); document.getElementById('confirmMessage').textContent = 'This is a confirmation dialog. Do you want to proceed?'; document.getElementById('confirmButton').onclick = () => { hideModal('confirmModal'); showToast('Action confirmed!', 'success'); }">
                    <i data-feather="help-circle" style="width: 16px; height: 16px; margin-right: 8px;"></i>
                    Show Confirm
                </button>
                <button class="btn btn-primary" onclick="addNotification()">
                    <i data-feather="bell" style="width: 16px; height: 16px; margin-right: 8px;"></i>
                    Add Notification
                </button>
            `;
            dashboard.appendChild(alertDemo);

            // Re-initialize feather icons for new elements
            feather.replace();
        });

        // Add new notification (demo function)
        function addNotification() {
            const dropdown = document.getElementById('notificationsDropdown');
            const newNotification = document.createElement('div');
            newNotification.className = 'notification-item unread';
            newNotification.innerHTML = `
                <div class="notification-title">New Demo Notification</div>
                <div class="notification-message">This is a dynamically added notification</div>
                <div class="notification-time">Just now</div>
            `;

            // Insert after header
            const header = dropdown.querySelector('.notifications-header');
            header.insertAdjacentElement('afterend', newNotification);

            // Update badge
            const badge = document.getElementById('notificationBadge');
            const currentCount = parseInt(badge.textContent) || 0;
            badge.textContent = currentCount + 1;
            badge.style.display = 'flex';

            showToast('New notification added!', 'success');
        }
    </script>
</body>
</html>
