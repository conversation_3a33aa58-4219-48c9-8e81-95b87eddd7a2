<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile App Design</title>
    <!-- Google Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&family=Open+Sans:wght@300;400;500;600;700&family=Lato:wght@300;400;700&family=Montserrat:wght@300;400;500;600;700&family=Nunito:wght@300;400;500;600;700&family=Source+Sans+Pro:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #007bff;
            --secondary-color: #6c757d;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
            --background-color: #ffffff;
            --text-color: #212529;
            --border-color: #dee2e6;
            --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            --font-size: 16px;
            --border-radius: 8px;
            --shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-family);
            font-size: var(--font-size);
            color: var(--text-color);
            background-color: var(--background-color);
            line-height: 1.6;
        }

        /* Material Icons Styling */
        .material-icons {
            font-family: 'Material Icons';
            font-weight: normal;
            font-style: normal;
            font-size: 24px;
            line-height: 1;
            letter-spacing: normal;
            text-transform: none;
            display: inline-block;
            white-space: nowrap;
            word-wrap: normal;
            direction: ltr;
            -webkit-font-feature-settings: 'liga';
            font-feature-settings: 'liga';
            -webkit-font-smoothing: antialiased;
        }

        .material-icons.md-18 { font-size: 18px; }
        .material-icons.md-20 { font-size: 20px; }
        .material-icons.md-24 { font-size: 24px; }
        .material-icons.md-36 { font-size: 36px; }
        .material-icons.md-48 { font-size: 48px; }

        .app-container {
            display: flex;
            height: 100vh;
            overflow: hidden;
            flex-direction: column;
        }

        /* Top Navbar */
        .top-navbar {
            background-color: var(--background-color);
            border-bottom: 1px solid var(--border-color);
            padding: 0 20px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 999;
            box-shadow: var(--shadow);
        }

        .navbar-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .navbar-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 12px;
            border-radius: var(--border-radius);
            background-color: var(--light-color);
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 14px;
        }

        .notification-btn {
            position: relative;
            background: none;
            border: none;
            padding: 8px;
            border-radius: var(--border-radius);
            cursor: pointer;
            color: var(--text-color);
            transition: all 0.3s ease;
        }

        .notification-btn:hover {
            background-color: var(--light-color);
        }

        .notification-badge {
            position: absolute;
            top: 2px;
            right: 2px;
            background-color: var(--danger-color);
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }

        .hamburger-btn {
            background: none;
            border: none;
            padding: 8px;
            border-radius: var(--border-radius);
            cursor: pointer;
            color: var(--text-color);
            display: none;
        }

        .main-wrapper {
            display: flex;
            flex: 1;
            overflow: hidden;
        }

        /* Sidebar Navigation */
        .sidebar {
            width: 250px;
            background-color: var(--dark-color);
            color: white;
            padding: 20px 0;
            transform: translateX(-100%);
            transition: transform 0.3s ease;
            position: fixed;
            height: 100vh;
            z-index: 1000;
        }

        .sidebar.active {
            transform: translateX(0);
        }

        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            margin-bottom: 20px;
        }

        .sidebar-nav {
            list-style: none;
        }

        .sidebar-nav li {
            margin-bottom: 5px;
        }

        .sidebar-nav a {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 20px;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .sidebar-nav a:hover,
        .sidebar-nav a.active {
            background-color: var(--primary-color);
            color: white;
        }

        .sidebar-nav a .material-icons {
            font-size: 20px;
        }

        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: var(--dark-color);
            display: flex;
            justify-content: space-around;
            padding: 10px 0;
            z-index: 1000;
        }

        .bottom-nav a {
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            text-align: center;
            padding: 8px;
            border-radius: var(--border-radius);
            transition: all 0.3s ease;
            flex: 1;
            max-width: 80px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
        }

        .bottom-nav a:hover,
        .bottom-nav a.active {
            background-color: var(--primary-color);
            color: white;
        }

        .bottom-nav a .material-icons {
            font-size: 20px;
        }

        .bottom-nav a span {
            font-size: 12px;
        }

        /* Notifications Dropdown */
        .notifications-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background-color: var(--background-color);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            width: 300px;
            max-height: 400px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }

        .notifications-dropdown.show {
            display: block;
        }

        .notifications-header {
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .notification-item {
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .notification-item:hover {
            background-color: var(--light-color);
        }

        .notification-item:last-child {
            border-bottom: none;
        }

        .notification-item.unread {
            background-color: rgba(0, 123, 255, 0.05);
            border-left: 3px solid var(--primary-color);
        }

        .notification-title {
            font-weight: 600;
            margin-bottom: 5px;
        }

        .notification-message {
            font-size: 14px;
            color: var(--secondary-color);
            margin-bottom: 5px;
        }

        .notification-time {
            font-size: 12px;
            color: var(--secondary-color);
        }

        /* User Dropdown */
        .user-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background-color: var(--background-color);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            width: 200px;
            z-index: 1000;
            display: none;
        }

        .user-dropdown.show {
            display: block;
        }

        .user-dropdown-item {
            padding: 12px 16px;
            cursor: pointer;
            transition: background-color 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
            border-bottom: 1px solid var(--border-color);
        }

        .user-dropdown-item:hover {
            background-color: var(--light-color);
        }

        .user-dropdown-item:last-child {
            border-bottom: none;
        }

        .user-dropdown-item .material-icons {
            font-size: 16px;
        }

        .user-info {
            position: relative;
            cursor: pointer;
        }

        /* Students Dropdown */
        .students-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            background-color: var(--dark-color);
            border: 1px solid rgba(255,255,255,0.1);
            border-radius: var(--border-radius);
            min-width: 200px;
            z-index: 1000;
            display: none;
        }

        .students-dropdown.show {
            display: block;
        }

        .students-dropdown a {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .students-dropdown a:hover {
            background-color: var(--primary-color);
            color: white;
        }

        .students-dropdown a:last-child {
            border-bottom: none;
        }

        .students-dropdown a .material-icons {
            font-size: 16px;
        }

        .sidebar-nav .dropdown-parent {
            position: relative;
        }

        /* Color Presets */
        .color-presets {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .color-preset {
            padding: 15px;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .color-preset:hover {
            border-color: var(--primary-color);
            transform: translateY(-2px);
        }

        .color-preset.active {
            border-color: var(--primary-color);
            background-color: rgba(0, 123, 255, 0.1);
        }

        .preset-colors {
            display: flex;
            justify-content: center;
            gap: 5px;
            margin-bottom: 8px;
        }

        .preset-color {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 2px solid white;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
        }

        .preset-name {
            font-size: 12px;
            font-weight: 600;
        }

        /* Calendar Styles */
        .calendar-container {
            background: var(--background-color);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 20px;
            margin-top: 20px;
        }

        .calendar-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .calendar-nav {
            background: none;
            border: none;
            padding: 8px;
            border-radius: var(--border-radius);
            cursor: pointer;
            color: var(--primary-color);
            font-size: 18px;
        }

        .calendar-nav:hover {
            background-color: var(--light-color);
        }

        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 1px;
            background-color: var(--border-color);
            border-radius: var(--border-radius);
            overflow: hidden;
        }

        .calendar-day-header {
            background-color: var(--primary-color);
            color: white;
            padding: 10px;
            text-align: center;
            font-weight: 600;
            font-size: 12px;
        }

        .calendar-day {
            background-color: var(--background-color);
            padding: 10px;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            position: relative;
            transition: background-color 0.3s ease;
        }

        .calendar-day:hover {
            background-color: var(--light-color);
        }

        .calendar-day.other-month {
            color: var(--secondary-color);
            background-color: #f8f9fa;
        }

        .calendar-day.today {
            background-color: var(--primary-color);
            color: white;
            font-weight: 600;
        }

        .calendar-day.present {
            background-color: var(--success-color);
            color: white;
        }

        .calendar-day.absent {
            background-color: var(--danger-color);
            color: white;
        }

        .calendar-day.partial {
            background-color: var(--warning-color);
            color: white;
        }

        .attendance-legend {
            display: flex;
            gap: 20px;
            margin-top: 15px;
            flex-wrap: wrap;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 3px;
        }

        /* Attendance Stats */
        .attendance-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: var(--background-color);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 20px;
            text-align: center;
            box-shadow: var(--shadow);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .stat-label {
            color: var(--secondary-color);
            font-size: 14px;
        }

        .stat-card.present .stat-number {
            color: var(--success-color);
        }

        .stat-card.absent .stat-number {
            color: var(--danger-color);
        }

        .stat-card.partial .stat-number {
            color: var(--warning-color);
        }

        .stat-card.total .stat-number {
            color: var(--primary-color);
        }

        /* Attendance Form */
        .attendance-form {
            background: var(--background-color);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 20px;
            margin-bottom: 20px;
        }

        .student-attendance-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid var(--border-color);
        }

        .student-attendance-item:last-child {
            border-bottom: none;
        }

        .student-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .student-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 14px;
        }

        .attendance-options {
            display: flex;
            gap: 10px;
        }

        .attendance-btn {
            padding: 6px 12px;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .attendance-btn.present {
            background-color: var(--success-color);
            color: white;
        }

        .attendance-btn.absent {
            background-color: var(--danger-color);
            color: white;
        }

        .attendance-btn.partial {
            background-color: var(--warning-color);
            color: white;
        }

        .attendance-btn:not(.active) {
            background-color: var(--light-color);
            color: var(--text-color);
        }

        /* Main Content */
        .main-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            margin-bottom: 70px;
        }

        .page {
            display: none;
        }

        .page.active {
            display: block;
        }

        /* Login Page */
        .login-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
        }

        .login-form {
            background: white;
            padding: 40px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            width: 100%;
            max-width: 400px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
        }

        .form-control {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: var(--font-size);
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: var(--font-size);
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            gap: 8px;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: #0056b3;
        }

        .btn-success {
            background-color: var(--success-color);
            color: white;
        }

        .btn-danger {
            background-color: var(--danger-color);
            color: white;
        }

        .btn-secondary {
            background-color: var(--secondary-color);
            color: white;
        }

        /* Table */
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .table th {
            background-color: var(--light-color);
            font-weight: 600;
        }

        /* Modal */
        .modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal.active {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background-color: white;
            padding: 30px;
            border-radius: var(--border-radius);
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .close {
            font-size: 24px;
            cursor: pointer;
            color: var(--secondary-color);
        }

        /* Toast */
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: var(--border-radius);
            color: white;
            z-index: 3000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }

        .toast.show {
            transform: translateX(0);
        }

        .toast.success {
            background-color: var(--success-color);
        }

        .toast.error {
            background-color: var(--danger-color);
        }

        .toast.info {
            background-color: var(--info-color);
        }

        /* Settings */
        .settings-group {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
        }

        .settings-group h3 {
            margin-bottom: 15px;
            color: var(--primary-color);
        }

        .color-picker {
            width: 50px;
            height: 40px;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
        }

        /* Responsive Design */
        @media (min-width: 768px) {
            .sidebar {
                position: static;
                transform: translateX(0);
            }

            .bottom-nav {
                display: none;
            }

            .main-content {
                margin-bottom: 0;
                margin-left: 0;
            }

            .hamburger-btn {
                display: none;
            }
        }

        @media (max-width: 767px) {
            .main-content {
                padding: 15px;
                margin-bottom: 70px;
            }

            .login-form {
                padding: 30px 20px;
                margin: 20px;
            }

            .modal-content {
                margin: 20px;
                padding: 20px;
            }

            .table {
                font-size: 14px;
            }

            .table th,
            .table td {
                padding: 8px;
            }

            .hamburger-btn {
                display: block;
            }

            .user-info span {
                display: none;
            }

            .notifications-dropdown {
                width: 280px;
                right: -20px;
            }
        }
    </style>
</head>
<body>
    <!-- Login Page -->
    <div id="loginPage" class="login-container">
        <form class="login-form" onsubmit="login(event)">
            <h2 style="text-align: center; margin-bottom: 30px; color: var(--primary-color);">Login</h2>
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" class="form-control" required>
            </div>
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" class="form-control" required>
            </div>
            <button type="submit" class="btn btn-primary" style="width: 100%;">Login</button>
        </form>
    </div>

    <!-- Main App -->
    <div id="mainApp" class="app-container" style="display: none;">
        <!-- Top Navbar -->
        <nav class="top-navbar">
            <div class="navbar-left">
                <button class="hamburger-btn" onclick="toggleSidebar()">
                    <span class="material-icons">menu</span>
                </button>
                <h3>My App</h3>
            </div>
            <div class="navbar-right">
                <div class="notification-btn" onclick="toggleNotifications()">
                    <span class="material-icons">notifications</span>
                    <span class="notification-badge" id="notificationBadge">3</span>
                    <!-- Notifications Dropdown -->
                    <div class="notifications-dropdown" id="notificationsDropdown">
                        <div class="notifications-header">
                            <span>Notifications</span>
                            <button onclick="markAllAsRead()" style="background: none; border: none; color: var(--primary-color); cursor: pointer; font-size: 12px;">Mark all as read</button>
                        </div>
                        <div class="notification-item unread">
                            <div class="notification-title">New User Registered</div>
                            <div class="notification-message">John Smith has joined the platform</div>
                            <div class="notification-time">2 minutes ago</div>
                        </div>
                        <div class="notification-item unread">
                            <div class="notification-title">System Update</div>
                            <div class="notification-message">New features are now available</div>
                            <div class="notification-time">1 hour ago</div>
                        </div>
                        <div class="notification-item unread">
                            <div class="notification-title">Backup Complete</div>
                            <div class="notification-message">Daily backup has been completed successfully</div>
                            <div class="notification-time">3 hours ago</div>
                        </div>
                    </div>
                </div>
                <div class="user-info" onclick="toggleUserDropdown()">
                    <div class="user-avatar" id="userAvatar">JD</div>
                    <span id="userName">John Doe</span>
                    <span class="material-icons md-18">expand_more</span>
                    <!-- User Dropdown -->
                    <div class="user-dropdown" id="userDropdown">
                        <div class="user-dropdown-item" onclick="showPage('profile')">
                            <span class="material-icons">person</span>
                            <span>My Profile</span>
                        </div>
                        <div class="user-dropdown-item" onclick="showPage('account-settings')">
                            <span class="material-icons">settings</span>
                            <span>Account Settings</span>
                        </div>
                        <div class="user-dropdown-item" onclick="showPage('preferences')">
                            <span class="material-icons">tune</span>
                            <span>Preferences</span>
                        </div>
                        <div class="user-dropdown-item" onclick="showPage('help')">
                            <span class="material-icons">help</span>
                            <span>Help & Support</span>
                        </div>
                        <div class="user-dropdown-item" onclick="logout()" style="color: var(--danger-color);">
                            <span class="material-icons">logout</span>
                            <span>Logout</span>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <div class="main-wrapper">
            <!-- Sidebar -->
            <nav class="sidebar" id="sidebar">
                <div class="sidebar-header">
                    <h3>My App</h3>
                </div>
                <ul class="sidebar-nav">
                    <li><a href="#" onclick="showPage('dashboard')" class="nav-link active">
                        <span class="material-icons">home</span>
                        <span>Dashboard</span>
                    </a></li>
                    <li class="dropdown-parent">
                        <a href="#" onclick="toggleStudentsDropdown(event)" class="nav-link">
                            <span class="material-icons">school</span>
                            <span>Students</span>
                            <span class="material-icons md-18" style="margin-left: auto;">expand_more</span>
                        </a>
                        <div class="students-dropdown" id="studentsDropdown">
                            <a href="#" onclick="showPage('students-list')">
                                <span class="material-icons">list</span>
                                <span>All Students</span>
                            </a>
                            <a href="#" onclick="showPage('add-student')">
                                <span class="material-icons">person_add</span>
                                <span>Add Student</span>
                            </a>
                            <a href="#" onclick="showPage('student-grades')">
                                <span class="material-icons">grade</span>
                                <span>Grades</span>
                            </a>
                            <a href="#" onclick="showPage('student-attendance')">
                                <span class="material-icons">event_available</span>
                                <span>Attendance</span>
                            </a>
                        </div>
                    </li>
                    <li><a href="#" onclick="showPage('users')" class="nav-link">
                        <span class="material-icons">people</span>
                        <span>Users</span>
                    </a></li>
                    <li><a href="#" onclick="showPage('settings')" class="nav-link">
                        <span class="material-icons">settings</span>
                        <span>Settings</span>
                    </a></li>
                    <li><a href="#" onclick="logout()">
                        <span class="material-icons">logout</span>
                        <span>Logout</span>
                    </a></li>
                </ul>
            </nav>

            <!-- Main Content -->
            <main class="main-content">
            <!-- Dashboard Page -->
            <div id="dashboard" class="page active">
                <h1>Dashboard</h1>
                <p>Welcome to your dashboard! This is a responsive mobile app design.</p>
                <div style="margin-top: 20px;">
                    <button class="btn btn-primary" onclick="showToast('Welcome to the dashboard!', 'info')">Show Info Toast</button>
                    <button class="btn btn-success" onclick="showToast('Success message!', 'success')">Show Success Toast</button>
                    <button class="btn btn-danger" onclick="showToast('Error message!', 'error')">Show Error Toast</button>
                </div>
            </div>

            <!-- Students Pages -->
            <div id="students-list" class="page">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h1>All Students</h1>
                    <button class="btn btn-primary" onclick="showPage('add-student')">
                        <i data-feather="user-plus" style="width: 16px; height: 16px;"></i>
                        Add Student
                    </button>
                </div>
                <table class="table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Grade</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>S001</td>
                            <td>Alice Johnson</td>
                            <td><EMAIL></td>
                            <td>A</td>
                            <td>
                                <button class="btn btn-primary" style="padding: 6px 12px; margin-right: 5px;">
                                    <i data-feather="edit" style="width: 14px; height: 14px;"></i>
                                    Edit
                                </button>
                                <button class="btn btn-danger" style="padding: 6px 12px;">
                                    <i data-feather="trash-2" style="width: 14px; height: 14px;"></i>
                                    Delete
                                </button>
                            </td>
                        </tr>
                        <tr>
                            <td>S002</td>
                            <td>Bob Wilson</td>
                            <td><EMAIL></td>
                            <td>B+</td>
                            <td>
                                <button class="btn btn-primary" style="padding: 6px 12px; margin-right: 5px;">
                                    <i data-feather="edit" style="width: 14px; height: 14px;"></i>
                                    Edit
                                </button>
                                <button class="btn btn-danger" style="padding: 6px 12px;">
                                    <i data-feather="trash-2" style="width: 14px; height: 14px;"></i>
                                    Delete
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div id="add-student" class="page">
                <h1>Add New Student</h1>
                <form style="max-width: 600px; margin-top: 20px;">
                    <div class="form-group">
                        <label for="studentName">Full Name</label>
                        <input type="text" id="studentName" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="studentEmail">Email</label>
                        <input type="email" id="studentEmail" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="studentPhone">Phone</label>
                        <input type="tel" id="studentPhone" class="form-control">
                    </div>
                    <div class="form-group">
                        <label for="studentGrade">Grade</label>
                        <select id="studentGrade" class="form-control">
                            <option value="A">A</option>
                            <option value="B+">B+</option>
                            <option value="B">B</option>
                            <option value="C+">C+</option>
                            <option value="C">C</option>
                        </select>
                    </div>
                    <button type="submit" class="btn btn-success">
                        <i data-feather="save" style="width: 16px; height: 16px;"></i>
                        Save Student
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="showPage('students-list')">
                        <i data-feather="arrow-left" style="width: 16px; height: 16px;"></i>
                        Back to List
                    </button>
                </form>
            </div>

            <div id="student-grades" class="page">
                <h1>Student Grades</h1>
                <p>Manage and view student grades and academic performance.</p>
                <div style="margin-top: 20px;">
                    <button class="btn btn-primary">
                        <i data-feather="plus" style="width: 16px; height: 16px;"></i>
                        Add Grade
                    </button>
                    <button class="btn btn-info">
                        <i data-feather="download" style="width: 16px; height: 16px;"></i>
                        Export Grades
                    </button>
                </div>
            </div>

            <div id="student-attendance" class="page">
                <h1>Student Attendance</h1>

                <!-- Attendance Stats -->
                <div class="attendance-stats">
                    <div class="stat-card present">
                        <div class="stat-number" id="presentCount">24</div>
                        <div class="stat-label">Present Today</div>
                    </div>
                    <div class="stat-card absent">
                        <div class="stat-number" id="absentCount">3</div>
                        <div class="stat-label">Absent Today</div>
                    </div>
                    <div class="stat-card partial">
                        <div class="stat-number" id="partialCount">2</div>
                        <div class="stat-label">Partial Attendance</div>
                    </div>
                    <div class="stat-card total">
                        <div class="stat-number" id="totalStudents">29</div>
                        <div class="stat-label">Total Students</div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div style="margin-bottom: 20px; display: flex; gap: 10px; flex-wrap: wrap;">
                    <button class="btn btn-primary" onclick="showAttendanceForm()">
                        <i data-feather="check-circle" style="width: 16px; height: 16px;"></i>
                        Mark Today's Attendance
                    </button>
                    <button class="btn btn-info" onclick="toggleCalendarView()">
                        <i data-feather="calendar" style="width: 16px; height: 16px;"></i>
                        <span id="calendarToggleText">Show Calendar</span>
                    </button>
                    <button class="btn btn-success" onclick="exportAttendance()">
                        <i data-feather="download" style="width: 16px; height: 16px;"></i>
                        Export Report
                    </button>
                </div>

                <!-- Attendance Form (Hidden by default) -->
                <div id="attendanceForm" class="attendance-form" style="display: none;">
                    <h3 style="margin-bottom: 20px;">Mark Attendance - <span id="attendanceDate"></span></h3>
                    <div id="studentAttendanceList">
                        <!-- Students will be populated here -->
                    </div>
                    <div style="text-align: right; margin-top: 20px;">
                        <button class="btn btn-success" onclick="saveAttendance()">
                            <i data-feather="save" style="width: 16px; height: 16px;"></i>
                            Save Attendance
                        </button>
                        <button class="btn btn-secondary" onclick="hideAttendanceForm()">
                            <i data-feather="x" style="width: 16px; height: 16px;"></i>
                            Cancel
                        </button>
                    </div>
                </div>

                <!-- Calendar View (Hidden by default) -->
                <div id="attendanceCalendar" class="calendar-container" style="display: none;">
                    <div class="calendar-header">
                        <button class="calendar-nav" onclick="changeMonth(-1)">
                            <i data-feather="chevron-left"></i>
                        </button>
                        <h3 id="calendarMonth">March 2024</h3>
                        <button class="calendar-nav" onclick="changeMonth(1)">
                            <i data-feather="chevron-right"></i>
                        </button>
                    </div>
                    <div class="calendar-grid" id="calendarGrid">
                        <!-- Calendar will be populated here -->
                    </div>
                    <div class="attendance-legend">
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: var(--success-color);"></div>
                            <span>Present</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: var(--danger-color);"></div>
                            <span>Absent</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: var(--warning-color);"></div>
                            <span>Partial</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background-color: var(--primary-color);"></div>
                            <span>Today</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profile Pages -->
            <div id="profile" class="page">
                <h1>My Profile</h1>
                <div style="max-width: 600px; margin-top: 20px;">
                    <div class="user-avatar" style="width: 80px; height: 80px; font-size: 24px; margin-bottom: 20px;" id="profileAvatar">JD</div>
                    <div class="form-group">
                        <label>Full Name</label>
                        <input type="text" class="form-control" value="John Doe" readonly>
                    </div>
                    <div class="form-group">
                        <label>Email</label>
                        <input type="email" class="form-control" value="<EMAIL>" readonly>
                    </div>
                    <div class="form-group">
                        <label>Role</label>
                        <input type="text" class="form-control" value="Administrator" readonly>
                    </div>
                    <button class="btn btn-primary">
                        <i data-feather="edit" style="width: 16px; height: 16px;"></i>
                        Edit Profile
                    </button>
                </div>
            </div>

            <div id="account-settings" class="page">
                <h1>Account Settings</h1>
                <p>Manage your account security and preferences.</p>
                <div style="margin-top: 20px;">
                    <button class="btn btn-primary">
                        <i data-feather="key" style="width: 16px; height: 16px;"></i>
                        Change Password
                    </button>
                    <button class="btn btn-info">
                        <i data-feather="shield" style="width: 16px; height: 16px;"></i>
                        Security Settings
                    </button>
                </div>
            </div>

            <div id="preferences" class="page">
                <h1>Preferences</h1>
                <p>Customize your application experience.</p>
                <div style="margin-top: 20px;">
                    <button class="btn btn-primary">
                        <i data-feather="bell" style="width: 16px; height: 16px;"></i>
                        Notification Settings
                    </button>
                    <button class="btn btn-info">
                        <i data-feather="globe" style="width: 16px; height: 16px;"></i>
                        Language & Region
                    </button>
                </div>
            </div>

            <div id="help" class="page">
                <h1>Help & Support</h1>
                <p>Get help and support for using the application.</p>
                <div style="margin-top: 20px;">
                    <button class="btn btn-primary">
                        <i data-feather="book" style="width: 16px; height: 16px;"></i>
                        Documentation
                    </button>
                    <button class="btn btn-info">
                        <i data-feather="message-circle" style="width: 16px; height: 16px;"></i>
                        Contact Support
                    </button>
                </div>
            </div>

            <!-- Users Page -->
            <div id="users" class="page">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h1>Users</h1>
                    <button class="btn btn-primary" onclick="showModal('addUserModal')">
                        <i data-feather="user-plus" style="width: 16px; height: 16px;"></i>
                        Add User
                    </button>
                </div>
                <table class="table" id="usersTable">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>1</td>
                            <td>John Doe</td>
                            <td><EMAIL></td>
                            <td>
                                <button class="btn btn-danger" onclick="confirmDelete(1)">
                                    <i data-feather="trash-2" style="width: 16px; height: 16px;"></i>
                                    Delete
                                </button>
                            </td>
                        </tr>
                        <tr>
                            <td>2</td>
                            <td>Jane Smith</td>
                            <td><EMAIL></td>
                            <td>
                                <button class="btn btn-danger" onclick="confirmDelete(2)">
                                    <i data-feather="trash-2" style="width: 16px; height: 16px;"></i>
                                    Delete
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Settings Page -->
            <div id="settings" class="page">
                <h1>Settings</h1>
                
                <div class="settings-group">
                    <h3>Colors</h3>

                    <!-- Color Presets -->
                    <h4 style="margin-bottom: 15px; color: var(--secondary-color);">Quick Themes</h4>
                    <div class="color-presets">
                        <div class="color-preset" onclick="applyColorPreset('default')">
                            <div class="preset-colors">
                                <div class="preset-color" style="background-color: #007bff;"></div>
                                <div class="preset-color" style="background-color: #ffffff;"></div>
                                <div class="preset-color" style="background-color: #212529;"></div>
                            </div>
                            <div class="preset-name">Default Blue</div>
                        </div>
                        <div class="color-preset" onclick="applyColorPreset('dark')">
                            <div class="preset-colors">
                                <div class="preset-color" style="background-color: #6f42c1;"></div>
                                <div class="preset-color" style="background-color: #1a1a1a;"></div>
                                <div class="preset-color" style="background-color: #ffffff;"></div>
                            </div>
                            <div class="preset-name">Dark Purple</div>
                        </div>
                        <div class="color-preset" onclick="applyColorPreset('green')">
                            <div class="preset-colors">
                                <div class="preset-color" style="background-color: #28a745;"></div>
                                <div class="preset-color" style="background-color: #f8f9fa;"></div>
                                <div class="preset-color" style="background-color: #343a40;"></div>
                            </div>
                            <div class="preset-name">Nature Green</div>
                        </div>
                        <div class="color-preset" onclick="applyColorPreset('orange')">
                            <div class="preset-colors">
                                <div class="preset-color" style="background-color: #fd7e14;"></div>
                                <div class="preset-color" style="background-color: #fff8f0;"></div>
                                <div class="preset-color" style="background-color: #495057;"></div>
                            </div>
                            <div class="preset-name">Warm Orange</div>
                        </div>
                        <div class="color-preset" onclick="applyColorPreset('teal')">
                            <div class="preset-colors">
                                <div class="preset-color" style="background-color: #20c997;"></div>
                                <div class="preset-color" style="background-color: #ffffff;"></div>
                                <div class="preset-color" style="background-color: #2d3748;"></div>
                            </div>
                            <div class="preset-name">Ocean Teal</div>
                        </div>
                        <div class="color-preset" onclick="applyColorPreset('pink')">
                            <div class="preset-colors">
                                <div class="preset-color" style="background-color: #e83e8c;"></div>
                                <div class="preset-color" style="background-color: #fdf2f8;"></div>
                                <div class="preset-color" style="background-color: #374151;"></div>
                            </div>
                            <div class="preset-name">Pink Blush</div>
                        </div>
                    </div>

                    <h4 style="margin: 25px 0 15px; color: var(--secondary-color);">Pastel Themes</h4>
                    <div class="color-presets">
                        <div class="color-preset" onclick="applyColorPreset('lavender')">
                            <div class="preset-colors">
                                <div class="preset-color" style="background-color: #a78bfa;"></div>
                                <div class="preset-color" style="background-color: #faf5ff;"></div>
                                <div class="preset-color" style="background-color: #4c1d95;"></div>
                            </div>
                            <div class="preset-name">Soft Lavender</div>
                        </div>
                        <div class="color-preset" onclick="applyColorPreset('mint')">
                            <div class="preset-colors">
                                <div class="preset-color" style="background-color: #6ee7b7;"></div>
                                <div class="preset-color" style="background-color: #f0fdf4;"></div>
                                <div class="preset-color" style="background-color: #064e3b;"></div>
                            </div>
                            <div class="preset-name">Fresh Mint</div>
                        </div>
                        <div class="color-preset" onclick="applyColorPreset('peach')">
                            <div class="preset-colors">
                                <div class="preset-color" style="background-color: #fbbf24;"></div>
                                <div class="preset-color" style="background-color: #fffbeb;"></div>
                                <div class="preset-color" style="background-color: #78350f;"></div>
                            </div>
                            <div class="preset-name">Warm Peach</div>
                        </div>
                        <div class="color-preset" onclick="applyColorPreset('sky')">
                            <div class="preset-colors">
                                <div class="preset-color" style="background-color: #7dd3fc;"></div>
                                <div class="preset-color" style="background-color: #f0f9ff;"></div>
                                <div class="preset-color" style="background-color: #0c4a6e;"></div>
                            </div>
                            <div class="preset-name">Sky Blue</div>
                        </div>
                        <div class="color-preset" onclick="applyColorPreset('rose')">
                            <div class="preset-colors">
                                <div class="preset-color" style="background-color: #fb7185;"></div>
                                <div class="preset-color" style="background-color: #fff1f2;"></div>
                                <div class="preset-color" style="background-color: #881337;"></div>
                            </div>
                            <div class="preset-name">Gentle Rose</div>
                        </div>
                        <div class="color-preset" onclick="applyColorPreset('coral')">
                            <div class="preset-colors">
                                <div class="preset-color" style="background-color: #ff7f7f;"></div>
                                <div class="preset-color" style="background-color: #fff5f5;"></div>
                                <div class="preset-color" style="background-color: #7f1d1d;"></div>
                            </div>
                            <div class="preset-name">Coral Dream</div>
                        </div>
                        <div class="color-preset" onclick="applyColorPreset('sage')">
                            <div class="preset-colors">
                                <div class="preset-color" style="background-color: #84cc16;"></div>
                                <div class="preset-color" style="background-color: #f7fee7;"></div>
                                <div class="preset-color" style="background-color: #365314;"></div>
                            </div>
                            <div class="preset-name">Sage Green</div>
                        </div>
                        <div class="color-preset" onclick="applyColorPreset('cream')">
                            <div class="preset-colors">
                                <div class="preset-color" style="background-color: #d97706;"></div>
                                <div class="preset-color" style="background-color: #fefce8;"></div>
                                <div class="preset-color" style="background-color: #451a03;"></div>
                            </div>
                            <div class="preset-name">Cream Delight</div>
                        </div>
                    </div>

                    <h4 style="margin: 25px 0 15px; color: var(--secondary-color);">Custom Colors</h4>
                    <div class="form-group">
                        <label for="primaryColor">Primary Color</label>
                        <input type="color" id="primaryColor" class="color-picker" value="#007bff" onchange="updateColor('--primary-color', this.value)">
                    </div>
                    <div class="form-group">
                        <label for="backgroundColor">Background Color</label>
                        <input type="color" id="backgroundColor" class="color-picker" value="#ffffff" onchange="updateColor('--background-color', this.value)">
                    </div>
                    <div class="form-group">
                        <label for="textColor">Text Color</label>
                        <input type="color" id="textColor" class="color-picker" value="#212529" onchange="updateColor('--text-color', this.value)">
                    </div>
                </div>

                <div class="settings-group">
                    <h3>Typography</h3>
                    <div class="form-group">
                        <label for="fontFamily">Font Family</label>
                        <select id="fontFamily" class="form-control" onchange="updateFont(this.value)">
                            <optgroup label="Google Fonts">
                                <option value="'Inter', sans-serif">Inter (Modern)</option>
                                <option value="'Poppins', sans-serif">Poppins (Friendly)</option>
                                <option value="'Roboto', sans-serif">Roboto (Clean)</option>
                                <option value="'Open Sans', sans-serif">Open Sans (Readable)</option>
                                <option value="'Lato', sans-serif">Lato (Elegant)</option>
                                <option value="'Montserrat', sans-serif">Montserrat (Bold)</option>
                                <option value="'Nunito', sans-serif">Nunito (Rounded)</option>
                                <option value="'Source Sans Pro', sans-serif">Source Sans Pro (Professional)</option>
                            </optgroup>
                            <optgroup label="System Fonts">
                                <option value="'Segoe UI', Tahoma, Geneva, Verdana, sans-serif">Segoe UI</option>
                                <option value="Arial, sans-serif">Arial</option>
                                <option value="'Times New Roman', serif">Times New Roman</option>
                                <option value="'Courier New', monospace">Courier New</option>
                            </optgroup>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="fontSize">Font Size</label>
                        <select id="fontSize" class="form-control" onchange="updateFontSize(this.value)">
                            <option value="14px">Small (14px)</option>
                            <option value="16px" selected>Medium (16px)</option>
                            <option value="18px">Large (18px)</option>
                            <option value="20px">Extra Large (20px)</option>
                        </select>
                    </div>
                </div>

                <button class="btn btn-success" onclick="saveSettings()">
                    <i data-feather="save" style="width: 16px; height: 16px;"></i>
                    Save Settings
                </button>
                <button class="btn btn-secondary" onclick="resetSettings()">
                    <i data-feather="refresh-cw" style="width: 16px; height: 16px;"></i>
                    Reset to Default
                </button>
            </div>
            </main>
        </div>

        <!-- Bottom Navigation -->
        <nav class="bottom-nav">
            <a href="#" onclick="showPage('dashboard')" class="nav-link active">
                <span class="material-icons">home</span>
                <span>Dashboard</span>
            </a>
            <a href="#" onclick="showPage('students-list')" class="nav-link">
                <span class="material-icons">school</span>
                <span>Students</span>
            </a>
            <a href="#" onclick="showPage('users')" class="nav-link">
                <span class="material-icons">people</span>
                <span>Users</span>
            </a>
            <a href="#" onclick="showPage('settings')" class="nav-link">
                <span class="material-icons">settings</span>
                <span>Settings</span>
            </a>
        </nav>
    </div>

    <!-- Add User Modal -->
    <div id="addUserModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Add New User</h3>
                <span class="close" onclick="hideModal('addUserModal')">&times;</span>
            </div>
            <form onsubmit="addUser(event)">
                <div class="form-group">
                    <label for="newUserName">Name</label>
                    <input type="text" id="newUserName" class="form-control" required>
                </div>
                <div class="form-group">
                    <label for="newUserEmail">Email</label>
                    <input type="email" id="newUserEmail" class="form-control" required>
                </div>
                <div style="text-align: right; margin-top: 20px;">
                    <button type="button" class="btn btn-secondary" onclick="hideModal('addUserModal')">
                        <i data-feather="x" style="width: 16px; height: 16px;"></i>
                        Cancel
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i data-feather="user-plus" style="width: 16px; height: 16px;"></i>
                        Add User
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Confirm Modal -->
    <div id="confirmModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Confirm Action</h3>
                <span class="close" onclick="hideModal('confirmModal')">&times;</span>
            </div>
            <p id="confirmMessage">Are you sure you want to perform this action?</p>
            <div style="text-align: right; margin-top: 20px;">
                <button type="button" class="btn btn-secondary" onclick="hideModal('confirmModal')">
                    <i data-feather="x" style="width: 16px; height: 16px;"></i>
                    Cancel
                </button>
                <button type="button" class="btn btn-danger" id="confirmButton">
                    <i data-feather="check" style="width: 16px; height: 16px;"></i>
                    Confirm
                </button>
            </div>
        </div>
    </div>

    <!-- Alert Modal -->
    <div id="alertModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="alertTitle">Alert</h3>
                <span class="close" onclick="hideModal('alertModal')">&times;</span>
            </div>
            <p id="alertMessage">This is an alert message.</p>
            <div style="text-align: right; margin-top: 20px;">
                <button type="button" class="btn btn-primary" onclick="hideModal('alertModal')">
                    <i data-feather="check" style="width: 16px; height: 16px;"></i>
                    OK
                </button>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div id="toastContainer"></div>

    <script>
        let currentUser = null;
        let users = [
            { id: 1, name: 'John Doe', email: '<EMAIL>' },
            { id: 2, name: 'Jane Smith', email: '<EMAIL>' }
        ];
        let nextUserId = 3;

        // Login functionality
        function login(event) {
            event.preventDefault();
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            // Simple validation (in real app, this would be server-side)
            if (username && password) {
                currentUser = username;
                document.getElementById('loginPage').style.display = 'none';
                document.getElementById('mainApp').style.display = 'flex';
                showToast('Login successful!', 'success');
                loadSettings();
                updateUserInfo(username);
            } else {
                showToast('Please enter valid credentials', 'error');
            }
        }

        // Update user info in navbar
        function updateUserInfo(username) {
            const initials = username.split(' ').map(name => name[0]).join('').toUpperCase();
            document.getElementById('userAvatar').textContent = initials;
            document.getElementById('userName').textContent = username;
        }

        // Logout functionality
        function logout() {
            currentUser = null;
            document.getElementById('loginPage').style.display = 'flex';
            document.getElementById('mainApp').style.display = 'none';
            showToast('Logged out successfully', 'info');
        }

        // Page navigation
        function showPage(pageId) {
            // Hide all pages
            const pages = document.querySelectorAll('.page');
            pages.forEach(page => page.classList.remove('active'));

            // Show selected page
            document.getElementById(pageId).classList.add('active');

            // Update navigation active states
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => link.classList.remove('active'));

            // Find and activate the correct nav link
            const activeLinks = document.querySelectorAll(`[onclick*="${pageId}"]`);
            activeLinks.forEach(link => link.classList.add('active'));

            // Close sidebar on mobile after navigation
            if (window.innerWidth < 768) {
                document.getElementById('sidebar').classList.remove('active');
            }
        }

        // Modal functionality
        function showModal(modalId) {
            document.getElementById(modalId).classList.add('active');
        }

        function hideModal(modalId) {
            document.getElementById(modalId).classList.remove('active');
        }

        // Toast functionality
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.textContent = message;

            document.body.appendChild(toast);

            // Show toast
            setTimeout(() => toast.classList.add('show'), 100);

            // Hide and remove toast
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => document.body.removeChild(toast), 300);
            }, 3000);
        }

        // User management
        function addUser(event) {
            event.preventDefault();
            const name = document.getElementById('newUserName').value;
            const email = document.getElementById('newUserEmail').value;

            const newUser = {
                id: nextUserId++,
                name: name,
                email: email
            };

            users.push(newUser);
            updateUsersTable();
            hideModal('addUserModal');
            showToast('User added successfully!', 'success');

            // Reset form
            document.getElementById('newUserName').value = '';
            document.getElementById('newUserEmail').value = '';
        }

        function confirmDelete(userId) {
            const user = users.find(u => u.id === userId);
            if (user) {
                document.getElementById('confirmMessage').textContent =
                    `Are you sure you want to delete user "${user.name}"?`;
                document.getElementById('confirmButton').onclick = () => deleteUser(userId);
                showModal('confirmModal');
            }
        }

        function deleteUser(userId) {
            users = users.filter(u => u.id !== userId);
            updateUsersTable();
            hideModal('confirmModal');
            showToast('User deleted successfully!', 'success');
        }

        function updateUsersTable() {
            const tbody = document.querySelector('#usersTable tbody');
            tbody.innerHTML = '';

            users.forEach(user => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${user.id}</td>
                    <td>${user.name}</td>
                    <td>${user.email}</td>
                    <td>
                        <button class="btn btn-danger" onclick="confirmDelete(${user.id})">
                            <i data-feather="trash-2" style="width: 16px; height: 16px;"></i>
                            Delete
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });


        }

        // Settings functionality
        function updateColor(property, value) {
            document.documentElement.style.setProperty(property, value);
        }

        function updateFont(fontFamily) {
            document.documentElement.style.setProperty('--font-family', fontFamily);
        }

        function updateFontSize(fontSize) {
            document.documentElement.style.setProperty('--font-size', fontSize);
        }

        function saveSettings() {
            const settings = {
                primaryColor: document.getElementById('primaryColor').value,
                backgroundColor: document.getElementById('backgroundColor').value,
                textColor: document.getElementById('textColor').value,
                fontFamily: document.getElementById('fontFamily').value,
                fontSize: document.getElementById('fontSize').value
            };

            localStorage.setItem('appSettings', JSON.stringify(settings));
            showToast('Settings saved successfully!', 'success');
        }

        function loadSettings() {
            const savedSettings = localStorage.getItem('appSettings');
            if (savedSettings) {
                const settings = JSON.parse(savedSettings);

                document.getElementById('primaryColor').value = settings.primaryColor;
                document.getElementById('backgroundColor').value = settings.backgroundColor;
                document.getElementById('textColor').value = settings.textColor;
                document.getElementById('fontFamily').value = settings.fontFamily;
                document.getElementById('fontSize').value = settings.fontSize;

                updateColor('--primary-color', settings.primaryColor);
                updateColor('--background-color', settings.backgroundColor);
                updateColor('--text-color', settings.textColor);
                updateFont(settings.fontFamily);
                updateFontSize(settings.fontSize);
            }
        }

        function resetSettings() {
            document.documentElement.style.setProperty('--primary-color', '#007bff');
            document.documentElement.style.setProperty('--background-color', '#ffffff');
            document.documentElement.style.setProperty('--text-color', '#212529');
            document.documentElement.style.setProperty('--font-family', "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif");
            document.documentElement.style.setProperty('--font-size', '16px');

            document.getElementById('primaryColor').value = '#007bff';
            document.getElementById('backgroundColor').value = '#ffffff';
            document.getElementById('textColor').value = '#212529';
            document.getElementById('fontFamily').value = "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif";
            document.getElementById('fontSize').value = '16px';

            localStorage.removeItem('appSettings');
            showToast('Settings reset to default!', 'info');
        }

        // Mobile sidebar toggle
        function toggleSidebar() {
            document.getElementById('sidebar').classList.toggle('active');
        }

        // Notifications functionality
        function toggleNotifications() {
            const dropdown = document.getElementById('notificationsDropdown');
            dropdown.classList.toggle('show');
        }

        function markAllAsRead() {
            const unreadItems = document.querySelectorAll('.notification-item.unread');
            unreadItems.forEach(item => item.classList.remove('unread'));
            document.getElementById('notificationBadge').style.display = 'none';
            showToast('All notifications marked as read', 'info');
        }

        // User dropdown functionality
        function toggleUserDropdown() {
            const dropdown = document.getElementById('userDropdown');
            dropdown.classList.toggle('show');
        }

        // Students dropdown functionality
        function toggleStudentsDropdown(event) {
            event.preventDefault();
            const dropdown = document.getElementById('studentsDropdown');
            dropdown.classList.toggle('show');
        }

        // Color preset functionality
        const colorPresets = {
            default: {
                primary: '#007bff',
                background: '#ffffff',
                text: '#212529'
            },
            dark: {
                primary: '#6f42c1',
                background: '#1a1a1a',
                text: '#ffffff'
            },
            green: {
                primary: '#28a745',
                background: '#f8f9fa',
                text: '#343a40'
            },
            orange: {
                primary: '#fd7e14',
                background: '#fff8f0',
                text: '#495057'
            },
            teal: {
                primary: '#20c997',
                background: '#ffffff',
                text: '#2d3748'
            },
            pink: {
                primary: '#e83e8c',
                background: '#fdf2f8',
                text: '#374151'
            },
            // Pastel themes
            lavender: {
                primary: '#a78bfa',
                background: '#faf5ff',
                text: '#4c1d95'
            },
            mint: {
                primary: '#6ee7b7',
                background: '#f0fdf4',
                text: '#064e3b'
            },
            peach: {
                primary: '#fbbf24',
                background: '#fffbeb',
                text: '#78350f'
            },
            sky: {
                primary: '#7dd3fc',
                background: '#f0f9ff',
                text: '#0c4a6e'
            },
            rose: {
                primary: '#fb7185',
                background: '#fff1f2',
                text: '#881337'
            },
            coral: {
                primary: '#ff7f7f',
                background: '#fff5f5',
                text: '#7f1d1d'
            },
            sage: {
                primary: '#84cc16',
                background: '#f7fee7',
                text: '#365314'
            },
            cream: {
                primary: '#d97706',
                background: '#fefce8',
                text: '#451a03'
            }
        };

        function applyColorPreset(presetName) {
            const preset = colorPresets[presetName];
            if (preset) {
                // Update CSS variables
                updateColor('--primary-color', preset.primary);
                updateColor('--background-color', preset.background);
                updateColor('--text-color', preset.text);

                // Update color pickers
                document.getElementById('primaryColor').value = preset.primary;
                document.getElementById('backgroundColor').value = preset.background;
                document.getElementById('textColor').value = preset.text;

                // Update active preset
                document.querySelectorAll('.color-preset').forEach(p => p.classList.remove('active'));
                event.target.closest('.color-preset').classList.add('active');

                showToast(`Applied ${presetName} theme!`, 'success');
            }
        }

        // Close dropdowns when clicking outside
        document.addEventListener('click', function(event) {
            const notificationBtn = document.querySelector('.notification-btn');
            const notificationsDropdown = document.getElementById('notificationsDropdown');
            const userInfo = document.querySelector('.user-info');
            const userDropdown = document.getElementById('userDropdown');
            const studentsDropdown = document.getElementById('studentsDropdown');

            // Close notifications dropdown
            if (!notificationBtn.contains(event.target)) {
                notificationsDropdown.classList.remove('show');
            }

            // Close user dropdown
            if (!userInfo.contains(event.target)) {
                userDropdown.classList.remove('show');
            }

            // Close students dropdown
            if (!event.target.closest('.dropdown-parent')) {
                studentsDropdown.classList.remove('show');
            }
        });

        // Alert functionality
        function showAlert(title, message) {
            document.getElementById('alertTitle').textContent = title;
            document.getElementById('alertMessage').textContent = message;
            showModal('alertModal');
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.classList.remove('active');
                }
            });
        }

        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize feather icons
            feather.replace();

            // Add demo buttons to dashboard for testing alerts
            const dashboard = document.getElementById('dashboard');
            const alertDemo = document.createElement('div');
            alertDemo.style.marginTop = '20px';
            alertDemo.innerHTML = `
                <h3>Demo Features:</h3>
                <button class="btn btn-info" onclick="showAlert('Information', 'This is an information alert!')">
                    <i data-feather="info" style="width: 16px; height: 16px; margin-right: 8px;"></i>
                    Show Alert
                </button>
                <button class="btn btn-warning" onclick="showModal('confirmModal'); document.getElementById('confirmMessage').textContent = 'This is a confirmation dialog. Do you want to proceed?'; document.getElementById('confirmButton').onclick = () => { hideModal('confirmModal'); showToast('Action confirmed!', 'success'); }">
                    <i data-feather="help-circle" style="width: 16px; height: 16px; margin-right: 8px;"></i>
                    Show Confirm
                </button>
                <button class="btn btn-primary" onclick="addNotification()">
                    <i data-feather="bell" style="width: 16px; height: 16px; margin-right: 8px;"></i>
                    Add Notification
                </button>
            `;
            dashboard.appendChild(alertDemo);

            // Re-initialize feather icons for new elements
            feather.replace();
        });

        // Add new notification (demo function)
        function addNotification() {
            const dropdown = document.getElementById('notificationsDropdown');
            const newNotification = document.createElement('div');
            newNotification.className = 'notification-item unread';
            newNotification.innerHTML = `
                <div class="notification-title">New Demo Notification</div>
                <div class="notification-message">This is a dynamically added notification</div>
                <div class="notification-time">Just now</div>
            `;

            // Insert after header
            const header = dropdown.querySelector('.notifications-header');
            header.insertAdjacentElement('afterend', newNotification);

            // Update badge
            const badge = document.getElementById('notificationBadge');
            const currentCount = parseInt(badge.textContent) || 0;
            badge.textContent = currentCount + 1;
            badge.style.display = 'flex';

            showToast('New notification added!', 'success');
        }

        // Attendance functionality
        let currentDate = new Date();
        let calendarVisible = false;
        let attendanceData = {};

        const students = [
            { id: 'S001', name: 'Alice Johnson', email: '<EMAIL>' },
            { id: 'S002', name: 'Bob Wilson', email: '<EMAIL>' },
            { id: 'S003', name: 'Carol Davis', email: '<EMAIL>' },
            { id: 'S004', name: 'David Brown', email: '<EMAIL>' },
            { id: 'S005', name: 'Emma Taylor', email: '<EMAIL>' }
        ];

        function showAttendanceForm() {
            const form = document.getElementById('attendanceForm');
            const dateStr = new Date().toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });

            document.getElementById('attendanceDate').textContent = dateStr;

            // Populate student list
            const listContainer = document.getElementById('studentAttendanceList');
            listContainer.innerHTML = '';

            students.forEach(student => {
                const item = document.createElement('div');
                item.className = 'student-attendance-item';
                item.innerHTML = `
                    <div class="student-info">
                        <div class="student-avatar">${student.name.split(' ').map(n => n[0]).join('')}</div>
                        <div>
                            <div style="font-weight: 600;">${student.name}</div>
                            <div style="font-size: 14px; color: var(--secondary-color);">${student.id}</div>
                        </div>
                    </div>
                    <div class="attendance-options">
                        <button class="attendance-btn present" onclick="markAttendance('${student.id}', 'present', this)">
                            <span class="material-icons" style="font-size: 12px;">check</span>
                            Present
                        </button>
                        <button class="attendance-btn absent" onclick="markAttendance('${student.id}', 'absent', this)">
                            <span class="material-icons" style="font-size: 12px;">close</span>
                            Absent
                        </button>
                        <button class="attendance-btn partial" onclick="markAttendance('${student.id}', 'partial', this)">
                            <span class="material-icons" style="font-size: 12px;">schedule</span>
                            Partial
                        </button>
                    </div>
                `;
                listContainer.appendChild(item);
            });

            form.style.display = 'block';
            feather.replace();
        }

        function hideAttendanceForm() {
            document.getElementById('attendanceForm').style.display = 'none';
        }

        function markAttendance(studentId, status, button) {
            // Remove active class from all buttons in this row
            const row = button.closest('.student-attendance-item');
            const buttons = row.querySelectorAll('.attendance-btn');
            buttons.forEach(btn => btn.classList.remove('active'));

            // Add active class to clicked button
            button.classList.add('active');

            // Store attendance data
            const today = new Date().toISOString().split('T')[0];
            if (!attendanceData[today]) {
                attendanceData[today] = {};
            }
            attendanceData[today][studentId] = status;
        }

        function saveAttendance() {
            const today = new Date().toISOString().split('T')[0];
            const todayData = attendanceData[today] || {};

            // Count attendance
            let present = 0, absent = 0, partial = 0;
            Object.values(todayData).forEach(status => {
                if (status === 'present') present++;
                else if (status === 'absent') absent++;
                else if (status === 'partial') partial++;
            });

            // Update stats
            document.getElementById('presentCount').textContent = present;
            document.getElementById('absentCount').textContent = absent;
            document.getElementById('partialCount').textContent = partial;

            hideAttendanceForm();
            showToast('Attendance saved successfully!', 'success');

            // Update calendar if visible
            if (calendarVisible) {
                generateCalendar();
            }
        }

        function toggleCalendarView() {
            const calendar = document.getElementById('attendanceCalendar');
            const toggleText = document.getElementById('calendarToggleText');

            calendarVisible = !calendarVisible;

            if (calendarVisible) {
                calendar.style.display = 'block';
                toggleText.textContent = 'Hide Calendar';
                generateCalendar();
            } else {
                calendar.style.display = 'none';
                toggleText.textContent = 'Show Calendar';
            }
        }

        function generateCalendar() {
            const grid = document.getElementById('calendarGrid');
            const monthElement = document.getElementById('calendarMonth');

            const year = currentDate.getFullYear();
            const month = currentDate.getMonth();

            monthElement.textContent = new Date(year, month).toLocaleDateString('en-US', {
                month: 'long',
                year: 'numeric'
            });

            // Clear grid
            grid.innerHTML = '';

            // Add day headers
            const dayHeaders = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
            dayHeaders.forEach(day => {
                const header = document.createElement('div');
                header.className = 'calendar-day-header';
                header.textContent = day;
                grid.appendChild(header);
            });

            // Get first day of month and number of days
            const firstDay = new Date(year, month, 1).getDay();
            const daysInMonth = new Date(year, month + 1, 0).getDate();
            const today = new Date();

            // Add empty cells for days before month starts
            for (let i = 0; i < firstDay; i++) {
                const emptyDay = document.createElement('div');
                emptyDay.className = 'calendar-day other-month';
                grid.appendChild(emptyDay);
            }

            // Add days of month
            for (let day = 1; day <= daysInMonth; day++) {
                const dayElement = document.createElement('div');
                dayElement.className = 'calendar-day';
                dayElement.textContent = day;

                const dateStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;

                // Check if it's today
                if (year === today.getFullYear() && month === today.getMonth() && day === today.getDate()) {
                    dayElement.classList.add('today');
                }

                // Check attendance data
                if (attendanceData[dateStr]) {
                    const statuses = Object.values(attendanceData[dateStr]);
                    const presentCount = statuses.filter(s => s === 'present').length;
                    const absentCount = statuses.filter(s => s === 'absent').length;
                    const partialCount = statuses.filter(s => s === 'partial').length;

                    if (presentCount > absentCount + partialCount) {
                        dayElement.classList.add('present');
                    } else if (absentCount > presentCount + partialCount) {
                        dayElement.classList.add('absent');
                    } else if (partialCount > 0) {
                        dayElement.classList.add('partial');
                    }
                }

                grid.appendChild(dayElement);
            }
        }

        function changeMonth(direction) {
            currentDate.setMonth(currentDate.getMonth() + direction);
            generateCalendar();
        }

        function exportAttendance() {
            showToast('Attendance report exported!', 'success');
        }
    </script>
</body>
</html>
