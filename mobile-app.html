<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile App Design</title>
    <style>
        :root {
            --primary-color: #007bff;
            --secondary-color: #6c757d;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
            --background-color: #ffffff;
            --text-color: #212529;
            --border-color: #dee2e6;
            --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            --font-size: 16px;
            --border-radius: 8px;
            --shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-family);
            font-size: var(--font-size);
            color: var(--text-color);
            background-color: var(--background-color);
            line-height: 1.6;
        }

        .app-container {
            display: flex;
            height: 100vh;
            overflow: hidden;
        }

        /* Sidebar Navigation */
        .sidebar {
            width: 250px;
            background-color: var(--dark-color);
            color: white;
            padding: 20px 0;
            transform: translateX(-100%);
            transition: transform 0.3s ease;
            position: fixed;
            height: 100vh;
            z-index: 1000;
        }

        .sidebar.active {
            transform: translateX(0);
        }

        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            margin-bottom: 20px;
        }

        .sidebar-nav {
            list-style: none;
        }

        .sidebar-nav li {
            margin-bottom: 5px;
        }

        .sidebar-nav a {
            display: block;
            padding: 12px 20px;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .sidebar-nav a:hover,
        .sidebar-nav a.active {
            background-color: var(--primary-color);
            color: white;
        }

        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: var(--dark-color);
            display: flex;
            justify-content: space-around;
            padding: 10px 0;
            z-index: 1000;
        }

        .bottom-nav a {
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            text-align: center;
            padding: 8px;
            border-radius: var(--border-radius);
            transition: all 0.3s ease;
            flex: 1;
            max-width: 80px;
        }

        .bottom-nav a:hover,
        .bottom-nav a.active {
            background-color: var(--primary-color);
            color: white;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            margin-bottom: 70px;
        }

        .page {
            display: none;
        }

        .page.active {
            display: block;
        }

        /* Login Page */
        .login-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
        }

        .login-form {
            background: white;
            padding: 40px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            width: 100%;
            max-width: 400px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
        }

        .form-control {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: var(--font-size);
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: var(--font-size);
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: #0056b3;
        }

        .btn-success {
            background-color: var(--success-color);
            color: white;
        }

        .btn-danger {
            background-color: var(--danger-color);
            color: white;
        }

        .btn-secondary {
            background-color: var(--secondary-color);
            color: white;
        }

        /* Table */
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .table th {
            background-color: var(--light-color);
            font-weight: 600;
        }

        /* Modal */
        .modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal.active {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background-color: white;
            padding: 30px;
            border-radius: var(--border-radius);
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .close {
            font-size: 24px;
            cursor: pointer;
            color: var(--secondary-color);
        }

        /* Toast */
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: var(--border-radius);
            color: white;
            z-index: 3000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }

        .toast.show {
            transform: translateX(0);
        }

        .toast.success {
            background-color: var(--success-color);
        }

        .toast.error {
            background-color: var(--danger-color);
        }

        .toast.info {
            background-color: var(--info-color);
        }

        /* Settings */
        .settings-group {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
        }

        .settings-group h3 {
            margin-bottom: 15px;
            color: var(--primary-color);
        }

        .color-picker {
            width: 50px;
            height: 40px;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
        }

        /* Responsive Design */
        @media (min-width: 768px) {
            .sidebar {
                position: static;
                transform: translateX(0);
            }
            
            .bottom-nav {
                display: none;
            }
            
            .main-content {
                margin-bottom: 0;
                margin-left: 0;
            }
        }

        @media (max-width: 767px) {
            .main-content {
                padding: 15px;
            }
            
            .login-form {
                padding: 30px 20px;
                margin: 20px;
            }
            
            .modal-content {
                margin: 20px;
                padding: 20px;
            }
            
            .table {
                font-size: 14px;
            }
            
            .table th,
            .table td {
                padding: 8px;
            }
        }
    </style>
</head>
<body>
    <!-- Login Page -->
    <div id="loginPage" class="login-container">
        <form class="login-form" onsubmit="login(event)">
            <h2 style="text-align: center; margin-bottom: 30px; color: var(--primary-color);">Login</h2>
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" class="form-control" required>
            </div>
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" class="form-control" required>
            </div>
            <button type="submit" class="btn btn-primary" style="width: 100%;">Login</button>
        </form>
    </div>

    <!-- Main App -->
    <div id="mainApp" class="app-container" style="display: none;">
        <!-- Sidebar -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h3>My App</h3>
            </div>
            <ul class="sidebar-nav">
                <li><a href="#" onclick="showPage('dashboard')" class="nav-link active">Dashboard</a></li>
                <li><a href="#" onclick="showPage('users')" class="nav-link">Users</a></li>
                <li><a href="#" onclick="showPage('settings')" class="nav-link">Settings</a></li>
                <li><a href="#" onclick="logout()">Logout</a></li>
            </ul>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Dashboard Page -->
            <div id="dashboard" class="page active">
                <h1>Dashboard</h1>
                <p>Welcome to your dashboard! This is a responsive mobile app design.</p>
                <div style="margin-top: 20px;">
                    <button class="btn btn-primary" onclick="showToast('Welcome to the dashboard!', 'info')">Show Info Toast</button>
                    <button class="btn btn-success" onclick="showToast('Success message!', 'success')">Show Success Toast</button>
                    <button class="btn btn-danger" onclick="showToast('Error message!', 'error')">Show Error Toast</button>
                </div>
            </div>

            <!-- Users Page -->
            <div id="users" class="page">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h1>Users</h1>
                    <button class="btn btn-primary" onclick="showModal('addUserModal')">Add User</button>
                </div>
                <table class="table" id="usersTable">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>1</td>
                            <td>John Doe</td>
                            <td><EMAIL></td>
                            <td>
                                <button class="btn btn-danger" onclick="confirmDelete(1)">Delete</button>
                            </td>
                        </tr>
                        <tr>
                            <td>2</td>
                            <td>Jane Smith</td>
                            <td><EMAIL></td>
                            <td>
                                <button class="btn btn-danger" onclick="confirmDelete(2)">Delete</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Settings Page -->
            <div id="settings" class="page">
                <h1>Settings</h1>
                
                <div class="settings-group">
                    <h3>Colors</h3>
                    <div class="form-group">
                        <label for="primaryColor">Primary Color</label>
                        <input type="color" id="primaryColor" class="color-picker" value="#007bff" onchange="updateColor('--primary-color', this.value)">
                    </div>
                    <div class="form-group">
                        <label for="backgroundColor">Background Color</label>
                        <input type="color" id="backgroundColor" class="color-picker" value="#ffffff" onchange="updateColor('--background-color', this.value)">
                    </div>
                    <div class="form-group">
                        <label for="textColor">Text Color</label>
                        <input type="color" id="textColor" class="color-picker" value="#212529" onchange="updateColor('--text-color', this.value)">
                    </div>
                </div>

                <div class="settings-group">
                    <h3>Typography</h3>
                    <div class="form-group">
                        <label for="fontFamily">Font Family</label>
                        <select id="fontFamily" class="form-control" onchange="updateFont(this.value)">
                            <option value="'Segoe UI', Tahoma, Geneva, Verdana, sans-serif">Segoe UI</option>
                            <option value="Arial, sans-serif">Arial</option>
                            <option value="'Times New Roman', serif">Times New Roman</option>
                            <option value="'Courier New', monospace">Courier New</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="fontSize">Font Size</label>
                        <select id="fontSize" class="form-control" onchange="updateFontSize(this.value)">
                            <option value="14px">Small (14px)</option>
                            <option value="16px" selected>Medium (16px)</option>
                            <option value="18px">Large (18px)</option>
                            <option value="20px">Extra Large (20px)</option>
                        </select>
                    </div>
                </div>

                <button class="btn btn-success" onclick="saveSettings()">Save Settings</button>
                <button class="btn btn-secondary" onclick="resetSettings()">Reset to Default</button>
            </div>
        </main>

        <!-- Bottom Navigation -->
        <nav class="bottom-nav">
            <a href="#" onclick="showPage('dashboard')" class="nav-link active">
                <div>📊</div>
                <div style="font-size: 12px;">Dashboard</div>
            </a>
            <a href="#" onclick="showPage('users')" class="nav-link">
                <div>👥</div>
                <div style="font-size: 12px;">Users</div>
            </a>
            <a href="#" onclick="showPage('settings')" class="nav-link">
                <div>⚙️</div>
                <div style="font-size: 12px;">Settings</div>
            </a>
            <a href="#" onclick="logout()">
                <div>🚪</div>
                <div style="font-size: 12px;">Logout</div>
            </a>
        </nav>
    </div>

    <!-- Add User Modal -->
    <div id="addUserModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Add New User</h3>
                <span class="close" onclick="hideModal('addUserModal')">&times;</span>
            </div>
            <form onsubmit="addUser(event)">
                <div class="form-group">
                    <label for="newUserName">Name</label>
                    <input type="text" id="newUserName" class="form-control" required>
                </div>
                <div class="form-group">
                    <label for="newUserEmail">Email</label>
                    <input type="email" id="newUserEmail" class="form-control" required>
                </div>
                <div style="text-align: right; margin-top: 20px;">
                    <button type="button" class="btn btn-secondary" onclick="hideModal('addUserModal')">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add User</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Confirm Modal -->
    <div id="confirmModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Confirm Action</h3>
                <span class="close" onclick="hideModal('confirmModal')">&times;</span>
            </div>
            <p id="confirmMessage">Are you sure you want to perform this action?</p>
            <div style="text-align: right; margin-top: 20px;">
                <button type="button" class="btn btn-secondary" onclick="hideModal('confirmModal')">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmButton">Confirm</button>
            </div>
        </div>
    </div>

    <!-- Alert Modal -->
    <div id="alertModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="alertTitle">Alert</h3>
                <span class="close" onclick="hideModal('alertModal')">&times;</span>
            </div>
            <p id="alertMessage">This is an alert message.</p>
            <div style="text-align: right; margin-top: 20px;">
                <button type="button" class="btn btn-primary" onclick="hideModal('alertModal')">OK</button>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div id="toastContainer"></div>

    <script>
        let currentUser = null;
        let users = [
            { id: 1, name: 'John Doe', email: '<EMAIL>' },
            { id: 2, name: 'Jane Smith', email: '<EMAIL>' }
        ];
        let nextUserId = 3;

        // Login functionality
        function login(event) {
            event.preventDefault();
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            // Simple validation (in real app, this would be server-side)
            if (username && password) {
                currentUser = username;
                document.getElementById('loginPage').style.display = 'none';
                document.getElementById('mainApp').style.display = 'flex';
                showToast('Login successful!', 'success');
                loadSettings();
            } else {
                showToast('Please enter valid credentials', 'error');
            }
        }

        // Logout functionality
        function logout() {
            currentUser = null;
            document.getElementById('loginPage').style.display = 'flex';
            document.getElementById('mainApp').style.display = 'none';
            showToast('Logged out successfully', 'info');
        }

        // Page navigation
        function showPage(pageId) {
            // Hide all pages
            const pages = document.querySelectorAll('.page');
            pages.forEach(page => page.classList.remove('active'));

            // Show selected page
            document.getElementById(pageId).classList.add('active');

            // Update navigation active states
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => link.classList.remove('active'));

            // Find and activate the correct nav link
            const activeLinks = document.querySelectorAll(`[onclick*="${pageId}"]`);
            activeLinks.forEach(link => link.classList.add('active'));

            // Close sidebar on mobile after navigation
            if (window.innerWidth < 768) {
                document.getElementById('sidebar').classList.remove('active');
            }
        }

        // Modal functionality
        function showModal(modalId) {
            document.getElementById(modalId).classList.add('active');
        }

        function hideModal(modalId) {
            document.getElementById(modalId).classList.remove('active');
        }

        // Toast functionality
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.textContent = message;

            document.body.appendChild(toast);

            // Show toast
            setTimeout(() => toast.classList.add('show'), 100);

            // Hide and remove toast
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => document.body.removeChild(toast), 300);
            }, 3000);
        }

        // User management
        function addUser(event) {
            event.preventDefault();
            const name = document.getElementById('newUserName').value;
            const email = document.getElementById('newUserEmail').value;

            const newUser = {
                id: nextUserId++,
                name: name,
                email: email
            };

            users.push(newUser);
            updateUsersTable();
            hideModal('addUserModal');
            showToast('User added successfully!', 'success');

            // Reset form
            document.getElementById('newUserName').value = '';
            document.getElementById('newUserEmail').value = '';
        }

        function confirmDelete(userId) {
            const user = users.find(u => u.id === userId);
            if (user) {
                document.getElementById('confirmMessage').textContent =
                    `Are you sure you want to delete user "${user.name}"?`;
                document.getElementById('confirmButton').onclick = () => deleteUser(userId);
                showModal('confirmModal');
            }
        }

        function deleteUser(userId) {
            users = users.filter(u => u.id !== userId);
            updateUsersTable();
            hideModal('confirmModal');
            showToast('User deleted successfully!', 'success');
        }

        function updateUsersTable() {
            const tbody = document.querySelector('#usersTable tbody');
            tbody.innerHTML = '';

            users.forEach(user => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${user.id}</td>
                    <td>${user.name}</td>
                    <td>${user.email}</td>
                    <td>
                        <button class="btn btn-danger" onclick="confirmDelete(${user.id})">Delete</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // Settings functionality
        function updateColor(property, value) {
            document.documentElement.style.setProperty(property, value);
        }

        function updateFont(fontFamily) {
            document.documentElement.style.setProperty('--font-family', fontFamily);
        }

        function updateFontSize(fontSize) {
            document.documentElement.style.setProperty('--font-size', fontSize);
        }

        function saveSettings() {
            const settings = {
                primaryColor: document.getElementById('primaryColor').value,
                backgroundColor: document.getElementById('backgroundColor').value,
                textColor: document.getElementById('textColor').value,
                fontFamily: document.getElementById('fontFamily').value,
                fontSize: document.getElementById('fontSize').value
            };

            localStorage.setItem('appSettings', JSON.stringify(settings));
            showToast('Settings saved successfully!', 'success');
        }

        function loadSettings() {
            const savedSettings = localStorage.getItem('appSettings');
            if (savedSettings) {
                const settings = JSON.parse(savedSettings);

                document.getElementById('primaryColor').value = settings.primaryColor;
                document.getElementById('backgroundColor').value = settings.backgroundColor;
                document.getElementById('textColor').value = settings.textColor;
                document.getElementById('fontFamily').value = settings.fontFamily;
                document.getElementById('fontSize').value = settings.fontSize;

                updateColor('--primary-color', settings.primaryColor);
                updateColor('--background-color', settings.backgroundColor);
                updateColor('--text-color', settings.textColor);
                updateFont(settings.fontFamily);
                updateFontSize(settings.fontSize);
            }
        }

        function resetSettings() {
            document.documentElement.style.setProperty('--primary-color', '#007bff');
            document.documentElement.style.setProperty('--background-color', '#ffffff');
            document.documentElement.style.setProperty('--text-color', '#212529');
            document.documentElement.style.setProperty('--font-family', "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif");
            document.documentElement.style.setProperty('--font-size', '16px');

            document.getElementById('primaryColor').value = '#007bff';
            document.getElementById('backgroundColor').value = '#ffffff';
            document.getElementById('textColor').value = '#212529';
            document.getElementById('fontFamily').value = "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif";
            document.getElementById('fontSize').value = '16px';

            localStorage.removeItem('appSettings');
            showToast('Settings reset to default!', 'info');
        }

        // Mobile sidebar toggle
        function toggleSidebar() {
            document.getElementById('sidebar').classList.toggle('active');
        }

        // Alert functionality
        function showAlert(title, message) {
            document.getElementById('alertTitle').textContent = title;
            document.getElementById('alertMessage').textContent = message;
            showModal('alertModal');
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.classList.remove('active');
                }
            });
        }

        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            // Add hamburger menu for mobile
            if (window.innerWidth < 768) {
                const header = document.createElement('div');
                header.style.cssText = `
                    position: fixed;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 60px;
                    background-color: var(--dark-color);
                    display: flex;
                    align-items: center;
                    padding: 0 20px;
                    z-index: 1001;
                    color: white;
                `;
                header.innerHTML = `
                    <button onclick="toggleSidebar()" style="background: none; border: none; color: white; font-size: 20px; cursor: pointer;">☰</button>
                    <h3 style="margin-left: 15px;">My App</h3>
                `;
                document.body.insertBefore(header, document.body.firstChild);

                // Adjust main content padding
                document.querySelector('.main-content').style.paddingTop = '80px';
            }

            // Add demo buttons to dashboard for testing alerts
            const dashboard = document.getElementById('dashboard');
            const alertDemo = document.createElement('div');
            alertDemo.style.marginTop = '20px';
            alertDemo.innerHTML = `
                <h3>Demo Features:</h3>
                <button class="btn btn-info" onclick="showAlert('Information', 'This is an information alert!')">Show Alert</button>
                <button class="btn btn-warning" onclick="showModal('confirmModal'); document.getElementById('confirmMessage').textContent = 'This is a confirmation dialog. Do you want to proceed?'; document.getElementById('confirmButton').onclick = () => { hideModal('confirmModal'); showToast('Action confirmed!', 'success'); }">Show Confirm</button>
            `;
            dashboard.appendChild(alertDemo);
        });
    </script>
</body>
</html>
