// Icon replacement mapping from Feather to Material Icons
const iconReplacements = {
    'user-plus': 'person_add',
    'edit': 'edit',
    'trash-2': 'delete',
    'save': 'save',
    'arrow-left': 'arrow_back',
    'plus': 'add',
    'download': 'download',
    'check-circle': 'check_circle',
    'calendar': 'calendar_today',
    'x': 'close',
    'check': 'check',
    'key': 'vpn_key',
    'shield': 'security',
    'bell': 'notifications',
    'globe': 'language',
    'book': 'menu_book',
    'message-circle': 'chat',
    'info': 'info',
    'help-circle': 'help',
    'refresh-cw': 'refresh',
    'clock': 'schedule'
};

// Function to replace all Feather icons with Material Icons
function replaceFeatherIcons() {
    // Get all elements with data-feather attribute
    const featherIcons = document.querySelectorAll('[data-feather]');
    
    featherIcons.forEach(icon => {
        const featherName = icon.getAttribute('data-feather');
        const materialName = iconReplacements[featherName] || featherName;
        
        // Create new Material Icon span
        const materialIcon = document.createElement('span');
        materialIcon.className = 'material-icons';
        materialIcon.textContent = materialName;
        
        // Copy styles if they exist
        const style = icon.getAttribute('style');
        if (style) {
            // Convert width/height to font-size for Material Icons
            if (style.includes('width: 12px') || style.includes('height: 12px')) {
                materialIcon.style.fontSize = '12px';
            } else if (style.includes('width: 14px') || style.includes('height: 14px')) {
                materialIcon.style.fontSize = '14px';
            } else if (style.includes('width: 16px') || style.includes('height: 16px')) {
                materialIcon.style.fontSize = '16px';
            } else if (style.includes('width: 18px') || style.includes('height: 18px')) {
                materialIcon.style.fontSize = '18px';
            } else if (style.includes('width: 20px') || style.includes('height: 20px')) {
                materialIcon.style.fontSize = '20px';
            }
            
            // Copy margin styles
            if (style.includes('margin-right')) {
                const marginMatch = style.match(/margin-right:\s*([^;]+)/);
                if (marginMatch) {
                    materialIcon.style.marginRight = marginMatch[1];
                }
            }
            
            if (style.includes('margin-left')) {
                const marginMatch = style.match(/margin-left:\s*([^;]+)/);
                if (marginMatch) {
                    materialIcon.style.marginLeft = marginMatch[1];
                }
            }
        }
        
        // Replace the icon
        icon.parentNode.replaceChild(materialIcon, icon);
    });
}

// Run the replacement when DOM is loaded
document.addEventListener('DOMContentLoaded', replaceFeatherIcons);
